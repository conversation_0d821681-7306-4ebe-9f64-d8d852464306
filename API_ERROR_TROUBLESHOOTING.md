# Troubleshooting API Error: "Unexpected token '<'"

## 🚨 **Error yang <PERSON>**

```
❌ Network error: SyntaxError: Unexpected token '<', "<br />
<b>"... is not valid JSON
```

## 🔍 **Ana<PERSON>is <PERSON>r**

Error ini terjadi ketika:
1. **API mengembalikan HTML error page** bukan JSON response
2. **Server error (404, 500, dll)** yang menampilkan error page
3. **CORS issue** yang di-block oleh browser
4. **API endpoint salah** atau tidak tersedia

## 🛠️ **Perbaikan yang Sudah Diterapkan**

### 1. **Enhanced Error Handling**
```typescript
// Check if response is ok
if (!res.ok) {
  throw new Error(`HTTP ${res.status}: ${res.statusText}`);
}

// Check content type
const contentType = res.headers.get('content-type');
console.log('Response content-type:', contentType);

// Get response text first
const responseText = await res.text();
console.log('Raw response:', responseText.substring(0, 500));

// Try to parse as JSON
try {
  json = JSON.parse(responseText);
} catch (parseError) {
  throw new Error('Server mengembalikan response yang tidak valid (bukan JSON)');
}
```

### 2. **API Connection Test**
- Tombol "Test Koneksi API" untuk debugging
- Detailed logging untuk troubleshooting
- Response validation sebelum parsing JSON

### 3. **Better Error Messages**
- Error spesifik berdasarkan jenis masalah
- Instruksi untuk user cara mengatasi
- Debug information di console

## 🔧 **Cara Troubleshooting**

### **Step 1: Test API Connection**
1. Buka halaman Scan Rapat
2. Klik tombol "Test Koneksi API"
3. Periksa console browser (F12 → Console)

### **Step 2: Periksa Console Logs**
```javascript
// Yang harus dicek:
- API Status: 200 OK (atau error code)
- Response content-type: application/json
- Raw response: harus JSON, bukan HTML
```

### **Step 3: Identifikasi Masalah**

#### **Jika Response HTML (Error Page):**
```html
<!-- Contoh response yang salah -->
<br />
<b>Fatal error</b>: ...
<!DOCTYPE html>
<html>...
```
**Solusi:** Periksa API endpoint dan server

#### **Jika HTTP 404:**
```
HTTP 404: Not Found
```
**Solusi:** Periksa URL API, pastikan endpoint benar

#### **Jika HTTP 500:**
```
HTTP 500: Internal Server Error
```
**Solusi:** Hubungi administrator server

#### **Jika CORS Error:**
```
Access to fetch at '...' from origin '...' has been blocked by CORS policy
```
**Solusi:** Server perlu mengatur CORS headers

## 🌐 **Verifikasi API Endpoints**

### **API Rapat:**
```
URL: https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023
Method: GET
Expected Response: JSON dengan field 'status' dan 'data'
```

### **API Peserta:**
```
URL: https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023
Method: POST
Content-Type: application/json
Expected Response: JSON dengan field 'status'
```

## 🧪 **Manual Testing**

### **Test dengan Browser:**
1. Buka URL API di browser baru:
   ```
   https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023
   ```

2. **Expected Result (Good):**
   ```json
   {
     "status": "success",
     "data": [...]
   }
   ```

3. **Problem Result (Bad):**
   ```html
   <br />
   <b>Fatal error</b>: Uncaught Error...
   ```

### **Test dengan Postman/Curl:**
```bash
# Test GET API Rapat
curl -X GET "https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023"

# Test POST API Peserta
curl -X POST "https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023" \
  -H "Content-Type: application/json" \
  -d '{"api_key":"absensiku_api_key_2023","rapat_id":"1","user_id":"123","status":"hadir","waktu_hadir":"2024-07-24 12:00:00"}'
```

## 🔧 **Kemungkinan Solusi**

### **1. Server-side Issues:**
- **PHP Error:** Periksa PHP error logs
- **Database Connection:** Pastikan database accessible
- **File Permissions:** Periksa permission file API
- **PHP Version:** Pastikan compatible

### **2. Network Issues:**
- **DNS Resolution:** Pastikan domain resolve dengan benar
- **SSL Certificate:** Periksa HTTPS certificate valid
- **Firewall:** Pastikan tidak di-block firewall

### **3. CORS Issues:**
- **Server Headers:** Tambahkan CORS headers di server
  ```php
  header('Access-Control-Allow-Origin: *');
  header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
  header('Access-Control-Allow-Headers: Content-Type');
  ```

### **4. API Key Issues:**
- **Invalid Key:** Periksa API key masih valid
- **Rate Limiting:** Pastikan tidak kena rate limit
- **Authentication:** Periksa sistem auth server

## 📱 **Testing di Aplikasi**

### **Development Mode:**
```bash
npm run dev
# Buka http://localhost:5174
# Test scan barcode
# Periksa console untuk error details
```

### **Production Mode:**
```bash
npm run build
npx cap build android
# Test di device/emulator
# Periksa logcat untuk error details
```

## 🚀 **Next Steps**

### **Immediate Actions:**
1. ✅ Test API endpoints manual di browser
2. ✅ Periksa server logs untuk PHP errors
3. ✅ Verifikasi CORS configuration
4. ✅ Test dengan different network/device

### **Long-term Improvements:**
- [ ] Implement retry mechanism
- [ ] Add offline capability
- [ ] Better error recovery
- [ ] API health monitoring

## 📞 **Kontak Support**

Jika masalah masih berlanjut:
1. **Capture console logs** (screenshot/copy)
2. **Test API manual** dan share hasil
3. **Periksa server logs** untuk error details
4. **Hubungi administrator server** untuk investigasi lebih lanjut

## 🔍 **Debug Commands**

```javascript
// Jalankan di console browser untuk debug
console.log('Testing API...');
fetch('https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023')
  .then(res => {
    console.log('Status:', res.status);
    console.log('Headers:', Object.fromEntries(res.headers.entries()));
    return res.text();
  })
  .then(text => {
    console.log('Response:', text);
    try {
      const json = JSON.parse(text);
      console.log('Parsed JSON:', json);
    } catch (e) {
      console.error('JSON Parse Error:', e);
    }
  })
  .catch(err => console.error('Fetch Error:', err));
```
