import React, { useState, useEffect, useRef } from 'react';
import { IonPage, IonContent, IonButton, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonText, IonToast, IonLoading } from '@ionic/react';
import { BrowserMultiFormatReader } from '@zxing/browser';
import { NotFoundException } from '@zxing/library';

const API_RAPAT = 'https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023';
const API_PESERTA = 'https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023';

const ScanRapat: React.FC = () => {
  const [scanResult, setScanResult] = useState('');
  const [rapat, setRapat] = useState<any>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMsg, setToastMsg] = useState('');
  const [loading, setLoading] = useState(false);
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const [cameraError, setCameraError] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [cameraStarted, setCameraStarted] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');
  const [availableCameras, setAvailableCameras] = useState<any[]>([]);
  const [selectedCameraId, setSelectedCameraId] = useState<string>('');
  const [alreadyAttended, setAlreadyAttended] = useState(false);

  const videoRef = useRef<HTMLVideoElement>(null);
  const codeReader = useRef<BrowserMultiFormatReader | null>(null);
  const controlsRef = useRef<any>(null);

  // Function untuk test koneksi API
  const testApiConnection = async () => {
    try {
      console.log('Testing API connection...');
      const response = await fetch(API_RAPAT);
      console.log('API Status:', response.status, response.statusText);
      console.log('API Headers:', Object.fromEntries(response.headers.entries()));

      const text = await response.text();
      console.log('API Response (first 200 chars):', text.substring(0, 200));

      if (text.includes('<html>') || text.includes('<!DOCTYPE')) {
        console.error('❌ API mengembalikan HTML page, bukan JSON');
        return false;
      }

      try {
        JSON.parse(text);
        console.log('✅ API response is valid JSON');
        return true;
      } catch (e) {
        console.error('❌ API response is not valid JSON');
        return false;
      }
    } catch (error) {
      console.error('❌ API connection failed:', error);
      return false;
    }
  };

  const handleScan = async (data: string | null) => {
    if (data && data !== scanResult) {
      setScanResult(data);
      setLoading(true);
      console.log('Processing barcode:', data);

      // Cari rapat berdasarkan barcode_value
      try {
        console.log('Fetching rapat data from:', API_RAPAT);
        const res = await fetch(API_RAPAT);

        // Check if response is ok
        if (!res.ok) {
          throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }

        // Check content type
        const contentType = res.headers.get('content-type');
        console.log('Response content-type:', contentType);

        // Get response text first
        const responseText = await res.text();
        console.log('Raw response:', responseText.substring(0, 500)); // Log first 500 chars

        // Try to parse as JSON
        let json;
        try {
          json = JSON.parse(responseText);
        } catch (parseError) {
          console.error('JSON Parse Error:', parseError);
          console.error('Response was:', responseText);
          throw new Error('Server mengembalikan response yang tidak valid (bukan JSON). Kemungkinan server error atau endpoint salah.');
        }

        console.log('Rapat API response:', json);

        if (json.status === 'success') {
          const rapatData = json.data.find((r: any) => r.barcode_value === data);

          if (rapatData) {
            console.log('Found rapat data:', rapatData);
            setRapat(rapatData);

            // Pastikan user data tersedia
            if (!user.id && !user.nik) {
              setToastMsg('Data user tidak ditemukan. Silakan login ulang.');
              setShowToast(true);
              return;
            }

            // Kirim ke API peserta untuk update status hadir
            const currentTime = new Date();
            const waktuHadir = currentTime.toISOString().slice(0, 19).replace('T', ' ');

            const pesertaPayload = {
              api_key: 'absensiku_api_key_2023',
              rapat_id: rapatData.id,
              user_id: user.id || user.nik,
              status: 'hadir',
              waktu_hadir: waktuHadir,
            };

            console.log('Sending peserta data:', pesertaPayload);
            console.log('To API:', API_PESERTA);

            const pesertaRes = await fetch(API_PESERTA, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              body: JSON.stringify(pesertaPayload),
            });

            // Check if response is ok
            if (!pesertaRes.ok) {
              throw new Error(`HTTP ${pesertaRes.status}: ${pesertaRes.statusText}`);
            }

            // Check content type
            const pesertaContentType = pesertaRes.headers.get('content-type');
            console.log('Peserta response content-type:', pesertaContentType);

            // Get response text first
            const pesertaResponseText = await pesertaRes.text();
            console.log('Peserta raw response:', pesertaResponseText.substring(0, 500));

            // Try to parse as JSON
            let pesertaJson;
            try {
              pesertaJson = JSON.parse(pesertaResponseText);
            } catch (parseError) {
              console.error('Peserta JSON Parse Error:', parseError);
              console.error('Peserta response was:', pesertaResponseText);

              // Check if it's a duplicate entry error
              if (pesertaResponseText.includes('Duplicate entry') && pesertaResponseText.includes('rapat_user_unique')) {
                // User sudah absen sebelumnya - ini sebenarnya sukses, bukan error
                setToastMsg('✅ Anda sudah melakukan absensi untuk rapat ini sebelumnya! Status: HADIR');
                setShowToast(true);
                setAlreadyAttended(true);
                console.log('✅ User sudah absen sebelumnya - duplicate entry detected');

                // Tetap tampilkan info rapat meskipun sudah absen
                setRapat(rapatData);
                return; // Exit function, don't throw error
              }

              // Check for other common PHP errors
              if (pesertaResponseText.includes('Fatal error') || pesertaResponseText.includes('mysqli_sql_exception')) {
                throw new Error('Database error pada server. Hubungi administrator.');
              }

              throw new Error('API peserta mengembalikan response yang tidak valid. Kemungkinan server error.');
            }

            console.log('Peserta API response:', pesertaJson);

            if (pesertaJson.status === 'success') {
              setToastMsg(`Absensi rapat berhasil! Waktu hadir: ${currentTime.toLocaleTimeString('id-ID')}`);
              console.log('✅ Absensi berhasil dicatat');
            } else {
              setToastMsg(pesertaJson.message || 'Gagal mencatat absensi rapat');
              console.error('❌ Gagal absensi:', pesertaJson);
            }
            setShowToast(true);
          } else {
            setToastMsg('Barcode tidak valid untuk rapat yang tersedia!');
            setShowToast(true);
            setRapat(null);
            console.log('❌ Barcode tidak ditemukan dalam data rapat');
          }
        } else {
          setToastMsg('Gagal mengambil data rapat: ' + (json.message || 'Unknown error'));
          setShowToast(true);
          console.error('❌ API rapat error:', json);
        }
      } catch (e: any) {
        let errorMessage = 'Terjadi kesalahan koneksi';

        if (e.message.includes('JSON')) {
          errorMessage = 'Server mengembalikan response yang tidak valid. Kemungkinan:\n• API endpoint salah\n• Server sedang maintenance\n• CORS issue';
        } else if (e.message.includes('HTTP 404')) {
          errorMessage = 'API endpoint tidak ditemukan (404). Periksa URL API.';
        } else if (e.message.includes('HTTP 500')) {
          errorMessage = 'Server error (500). Hubungi administrator.';
        } else if (e.message.includes('Failed to fetch')) {
          errorMessage = 'Tidak dapat terhubung ke server. Periksa koneksi internet.';
        } else {
          errorMessage = 'Error: ' + e.message;
        }

        setToastMsg(errorMessage);
        setShowToast(true);
        console.error('❌ Network error:', e);

        // Tambahkan debug info ke UI
        setDebugInfo(`Error: ${e.message}`);
      } finally {
        setLoading(false);
      }
    }
  };



  // Inisialisasi kamera dan scanner
  const startScanning = async () => {
    try {
      setCameraError('');
      setLoading(true);

      // Hentikan scanning sebelumnya jika ada
      if (controlsRef.current) {
        controlsRef.current.stop();
        controlsRef.current = null;
      }

      if (!codeReader.current) {
        codeReader.current = new BrowserMultiFormatReader();
      }

      // Dapatkan daftar perangkat video
      const videoInputDevices = await BrowserMultiFormatReader.listVideoInputDevices();

      console.log('Available cameras:', videoInputDevices);
      setAvailableCameras(videoInputDevices);
      setDebugInfo(`Ditemukan ${videoInputDevices.length} kamera`);

      if (videoInputDevices.length === 0) {
        throw new Error('Tidak ada kamera yang ditemukan');
      }

      // Pilih kamera yang dipilih user atau kamera belakang jika tersedia, atau kamera pertama
      let selectedDeviceId = selectedCameraId || videoInputDevices[0].deviceId;
      let selectedCamera = videoInputDevices.find(d => d.deviceId === selectedDeviceId) || videoInputDevices[0];

      // Jika belum ada yang dipilih, cari kamera belakang
      if (!selectedCameraId) {
        const backCamera = videoInputDevices.find((device: any) =>
          device.label.toLowerCase().includes('back') ||
          device.label.toLowerCase().includes('rear') ||
          device.label.toLowerCase().includes('environment')
        );

        if (backCamera) {
          selectedDeviceId = backCamera.deviceId;
          selectedCamera = backCamera;
          setSelectedCameraId(selectedDeviceId);
          console.log('Using back camera:', backCamera.label);
          setDebugInfo(`Menggunakan kamera belakang: ${backCamera.label}`);
        } else {
          setSelectedCameraId(selectedDeviceId);
          console.log('Using default camera:', videoInputDevices[0].label);
          setDebugInfo(`Menggunakan kamera default: ${videoInputDevices[0].label}`);
        }
      } else {
        console.log('Using selected camera:', selectedCamera.label);
        setDebugInfo(`Menggunakan kamera: ${selectedCamera.label}`);
      }

      // Mulai scanning
      console.log('Starting camera with device ID:', selectedDeviceId);

      const controls = await codeReader.current.decodeFromVideoDevice(
        selectedDeviceId,
        videoRef.current!,
        (result, error) => {
          if (result) {
            const text = result.getText();
            console.log('Barcode detected:', text);
            if (text && text !== scanResult && isScanning) {
              setIsScanning(false);
              handleScan(text);
            }
          }

          if (error && !(error instanceof NotFoundException)) {
            console.error('Scan error:', error);
          }
        }
      );

      controlsRef.current = controls;
      console.log('Camera started successfully');

      setIsScanning(true);
      setCameraStarted(true);

    } catch (err: any) {
      console.error('Camera start error:', err);
      if (err.name === 'NotAllowedError') {
        setCameraError('Izin kamera ditolak. Silakan berikan izin kamera di pengaturan browser.');
      } else if (err.name === 'NotFoundError') {
        setCameraError('Kamera tidak ditemukan pada perangkat ini.');
      } else {
        setCameraError('Gagal mengakses kamera: ' + (err.message || 'Unknown error'));
      }
    } finally {
      setLoading(false);
    }
  };

  // Hentikan scanning
  const stopScanning = () => {
    if (controlsRef.current) {
      try {
        controlsRef.current.stop();
      } catch (e) {
        console.error('Error stopping scanner:', e);
      }
      controlsRef.current = null;
    }
    setIsScanning(false);
    setCameraStarted(false);
  };

  // Reset scanning state ketika scan ulang
  const resetScan = () => {
    setScanResult('');
    setRapat(null);
    setCameraError('');
    setAlreadyAttended(false);
    stopScanning();
    setTimeout(() => {
      startScanning();
    }, 100);
  };

  // Mulai scanning saat komponen dimount
  useEffect(() => {
    startScanning();

    // Cleanup saat komponen unmount
    return () => {
      stopScanning();
    };
  }, []);

  // Cleanup saat scanResult berubah
  useEffect(() => {
    if (scanResult && rapat) {
      stopScanning();
    }
  }, [scanResult, rapat]);

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Scan Barcode Rapat</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <div style={{ width: '100%', maxWidth: 400, margin: '0 auto' }}>
              {!cameraError ? (
                <div style={{ position: 'relative' }}>
                  <video
                    ref={videoRef}
                    style={{
                      width: '100%',
                      height: 'auto',
                      borderRadius: '8px',
                      backgroundColor: '#000'
                    }}
                    autoPlay
                    playsInline
                    muted
                  />
                  {!cameraStarted && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'rgba(0,0,0,0.7)',
                      color: 'white',
                      borderRadius: '8px'
                    }}>
                      <p>Memulai kamera...</p>
                    </div>
                  )}
                  {isScanning && cameraStarted && (
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      border: '2px solid #3880ff',
                      width: '200px',
                      height: '200px',
                      borderRadius: '8px',
                      pointerEvents: 'none'
                    }} />
                  )}
                </div>
              ) : (
                <div style={{
                  padding: '20px',
                  textAlign: 'center',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '8px',
                  minHeight: '200px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <IonText color="danger">
                    <p>Kamera tidak dapat diakses</p>
                  </IonText>
                </div>
              )}
            </div>

            {cameraError && (
              <div style={{ marginTop: 16 }}>
                <IonText color="danger"><p>{cameraError}</p></IonText>
                <IonButton
                  expand="block"
                  fill="outline"
                  onClick={startScanning}
                  style={{ marginTop: 8 }}
                >
                  Coba Lagi
                </IonButton>
              </div>
            )}

            {scanResult && !rapat && !loading && (
              <div style={{ marginTop: 16 }}>
                <IonText color="warning">
                  <p><strong>Hasil scan:</strong> {scanResult}</p>
                  <p>Barcode tidak ditemukan dalam data rapat yang tersedia.</p>
                </IonText>
              </div>
            )}

            {loading && scanResult && (
              <div style={{ marginTop: 16 }}>
                <IonText color="primary">
                  <p><strong>Memproses barcode:</strong> {scanResult}</p>
                  <p>Mencari data rapat dan mencatat kehadiran...</p>
                </IonText>
              </div>
            )}

            {rapat && (
              <div style={{ marginTop: 16 }}>
                <IonText color="success">
                  <h2>✅ {rapat.judul}</h2>
                </IonText>
                <div style={{
                  backgroundColor: '#f0f9ff',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '1px solid #3880ff'
                }}>
                  <p><b>📅 Tanggal:</b> {rapat.tanggal}</p>
                  <p><b>🕐 Waktu:</b> {rapat.waktu_mulai} - {rapat.waktu_selesai}</p>
                  <p><b>📍 Lokasi:</b> {rapat.lokasi}</p>
                  <p><b>📝 Deskripsi:</b> {rapat.deskripsi}</p>
                  <div style={{
                    marginTop: '8px',
                    padding: '8px',
                    backgroundColor: '#d4edda',
                    borderRadius: '4px',
                    border: '1px solid #28a745'
                  }}>
                    <IonText color="success">
                      <p><b>✅ Status: HADIR</b></p>
                      <p><b>🕐 Waktu Absen:</b> {new Date().toLocaleString('id-ID')}</p>
                    </IonText>
                  </div>
                </div>
              </div>
            )}

            {availableCameras.length > 1 && (
              <div style={{ marginTop: 16 }}>
                <IonText color="primary"><h4>Pilih Kamera:</h4></IonText>
                {availableCameras.map((camera, index) => (
                  <IonButton
                    key={camera.deviceId}
                    fill={selectedCameraId === camera.deviceId ? "solid" : "outline"}
                    size="small"
                    onClick={() => {
                      setSelectedCameraId(camera.deviceId);
                      resetScan();
                    }}
                    style={{ margin: '4px' }}
                  >
                    Kamera {index + 1}
                  </IonButton>
                ))}
              </div>
            )}

            {debugInfo && (
              <div style={{ marginTop: 16 }}>
                <IonText color="medium">
                  <p><small>{debugInfo}</small></p>
                </IonText>
              </div>
            )}

            {availableCameras.length > 1 && (
              <div style={{ marginTop: 16 }}>
                <IonText color="primary"><h4>Pilih Kamera:</h4></IonText>
                {availableCameras.map((camera, index) => (
                  <IonButton
                    key={camera.deviceId}
                    fill={selectedCameraId === camera.deviceId ? "solid" : "outline"}
                    size="small"
                    onClick={() => {
                      setSelectedCameraId(camera.deviceId);
                      resetScan();
                    }}
                    style={{ margin: '4px' }}
                  >
                    Kamera {index + 1}
                  </IonButton>
                ))}
              </div>
            )}

            <div style={{ marginTop: 16 }}>
              <IonButton
                expand="block"
                onClick={resetScan}
                disabled={loading}
                color="primary"
              >
                Scan Ulang
              </IonButton>

              <IonButton
                expand="block"
                fill="outline"
                onClick={testApiConnection}
                disabled={loading}
                color="secondary"
                style={{ marginTop: 8 }}
              >
                Test Koneksi API
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>
        <IonToast isOpen={showToast} onDidDismiss={() => setShowToast(false)} message={toastMsg} duration={2000} color="primary" />
        <IonLoading isOpen={loading} message="Memproses..." />
      </IonContent>
    </IonPage>
  );
};

export default ScanRapat; 