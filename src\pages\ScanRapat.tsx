import React, { useState } from 'react';
import { IonPage, IonContent, IonButton, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonText, IonToast, IonLoading } from '@ionic/react';
// @ts-ignore
import { QrReader } from 'react-qr-reader';

const API_RAPAT = 'https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023';
const API_PESERTA = 'https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023';

const ScanRapat: React.FC = () => {
  const [scanResult, setScanResult] = useState('');
  const [rapat, setRapat] = useState<any>(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMsg, setToastMsg] = useState('');
  const [loading, setLoading] = useState(false);
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const [cameraError, setCameraError] = useState('');

  const handleScan = async (data: string | null) => {
    if (data && data !== scanResult) {
      setScanResult(data);
      setLoading(true);
      // Cari rapat berdasarkan barcode_value
      try {
        const res = await fetch(API_RAPAT);
        const json = await res.json();
        if (json.status === 'success') {
          const rapatData = json.data.find((r: any) => r.barcode_value === data);
          if (rapatData) {
            setRapat(rapatData);
            // Kirim ke API peserta
            const pesertaPayload = {
              api_key: 'absensiku_api_key_2023',
              rapat_id: rapatData.id,
              user_id: user.id || user.nik,
              status: 'hadir',
              waktu_hadir: new Date().toISOString().slice(0, 19).replace('T', ' '),
            };
            const pesertaRes = await fetch(API_PESERTA, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(pesertaPayload),
            });
            const pesertaJson = await pesertaRes.json();
            if (pesertaJson.status === 'success') {
              setToastMsg('Absensi rapat berhasil!');
            } else {
              setToastMsg(pesertaJson.message || 'Gagal absen rapat');
            }
            setShowToast(true);
          } else {
            setToastMsg('Barcode tidak valid untuk rapat!');
            setShowToast(true);
            setRapat(null);
          }
        } else {
          setToastMsg('Gagal mengambil data rapat');
          setShowToast(true);
        }
      } catch (e) {
        setToastMsg('Terjadi kesalahan koneksi');
        setShowToast(true);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleError = (err: any) => {
    setToastMsg('Gagal mengakses kamera: ' + err);
    setShowToast(true);
  };

  const handleResult = async (result: any, error: any) => {
    if (!!result) {
      const text = result?.text;
      if (text && text !== scanResult) {
        await handleScan(text);
      }
    }
    if (!!error) {
      setCameraError('Gagal mengakses kamera: ' + (error.message || error.name || 'Unknown error'));
      console.error('Camera error:', error);
    }
  };

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Scan Barcode Rapat</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <div style={{ width: '100%', maxWidth: 400, margin: '0 auto' }}>
              <QrReader
                onResult={handleResult}
              />
            </div>
            {cameraError && (
              <IonText color="danger"><p>{cameraError}</p></IonText>
            )}
            {rapat && (
              <div style={{ marginTop: 16 }}>
                <IonText color="primary"><h2>{rapat.judul}</h2></IonText>
                <p><b>Tanggal:</b> {rapat.tanggal}</p>
                <p><b>Waktu:</b> {rapat.waktu_mulai} - {rapat.waktu_selesai}</p>
                <p><b>Lokasi:</b> {rapat.lokasi}</p>
                <p><b>Deskripsi:</b> {rapat.deskripsi}</p>
              </div>
            )}
            <IonButton expand="block" onClick={() => { setScanResult(''); setRapat(null); }}>Scan Ulang</IonButton>
          </IonCardContent>
        </IonCard>
        <IonToast isOpen={showToast} onDidDismiss={() => setShowToast(false)} message={toastMsg} duration={2000} color="primary" />
        <IonLoading isOpen={loading} message="Memproses..." />
      </IonContent>
    </IonPage>
  );
};

export default ScanRapat; 