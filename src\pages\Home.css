.home-bg {
  --background: linear-gradient(135deg, #f0fdfa 0%, #e0e7ff 100%);
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.home-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding-top: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.home-profile-card {
  width: 100%;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  margin-bottom: 24px;
  background: #fff;
}

.home-profile-header {
  display: flex;
  align-items: center;
  padding: 24px 18px 10px 18px;
  border-bottom: 1px solid #f1f5f9;
  background: transparent;
}

.home-avatar {
  width: 72px;
  height: 72px;
  margin-right: 18px;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  background: #f3f4f6;
}

.home-profile-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.home-nama {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 2px;
}

.home-jabatan {
  font-size: 1.05rem;
  color: #64748b;
}

.home-card-content {
  padding: 18px 18px 10px 18px;
  text-align: center;
}

.home-desc {
  color: #64748b;
  font-size: 1rem;
  margin: 10px 0 18px 0;
}

.home-logout-btn {
  margin-top: 12px;
  font-weight: 600;
  border-radius: 24px;
  letter-spacing: 0.5px;
  font-size: 1.1rem;
}

.home2-bg {
  --background: #fff;
  min-height: 100vh;
  padding: 0;
  animation: fadeIn 0.8s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: none; }
}

.home2-header, .home2-header-content, .home2-nama, .home2-jabatan {
  animation: fadeIn 1s;
}

/* Tombol kamera interaktif */
.home2-camera-btn {
  transition: transform 0.15s, box-shadow 0.2s;
  position: relative;
  overflow: hidden;
  animation: cameraPulse 1.5s infinite;
}

@keyframes cameraPulse {
  0% { box-shadow: 0 0 0 0 rgba(24,128,255,0.18); }
  70% { box-shadow: 0 0 0 24px rgba(24,128,255,0.01); }
  100% { box-shadow: 0 0 0 0 rgba(24,128,255,0.18); }
}
.home2-camera-btn:active {
  transform: scale(0.93);
  box-shadow: 0 2px 8px rgba(24,128,255,0.18);
}
.home2-camera-btn::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 0;
  height: 0;
  background: rgba(255,255,255,0.25);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
  pointer-events: none;
}
.home2-camera-btn:active::after {
  width: 180px;
  height: 180px;
}

/* Menu utama interaktif */
.home2-menu-btn {
  transition: box-shadow 0.2s, transform 0.15s, background 0.2s;
  will-change: transform;
  user-select: none;
}
.home2-menu-btn:active {
  box-shadow: 0 8px 32px rgba(24,128,255,0.18);
  background: #e0e7ff;
  transform: scale(1.07);
}
.home2-menu-btn ion-icon {
  transition: transform 0.18s;
}
.home2-menu-btn:active ion-icon {
  animation: bounce 0.4s;
}
@keyframes bounce {
  0% { transform: scale(1); }
  30% { transform: scale(1.25) translateY(-6px); }
  60% { transform: scale(0.95) translateY(2px); }
  100% { transform: scale(1); }
}

/* Status absen highlight */
.home2-status-box {
  transition: box-shadow 0.2s, background 0.2s, color 0.2s;
}
.home2-status-box.status-highlight {
  background: #e0e7ff;
  color: #1880ff;
  box-shadow: 0 0 0 4px #1880ff33;
  animation: highlightFade 1.2s;
}
@keyframes highlightFade {
  0% { background: #1880ff; color: #fff; }
  100% { background: #e0e7ff; color: #1880ff; }
}

/* Tooltip sederhana untuk status */
.home2-status-box[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 50%;
  top: 0;
  transform: translate(-50%, -120%);
  background: #222;
  color: #fff;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  white-space: nowrap;
  z-index: 10;
  opacity: 0.95;
}

/* Transisi halus pada semua elemen utama */
.home2-main, .home2-menu-row, .home2-status-row, .home2-status-title {
  transition: all 0.3s;
}

.home2-header-content {
  width: 100%;
  max-width: 420px;
  padding: 32px 0 18px 0;
  text-align: center;
}

.home2-nama {
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 2px 0;
}

.home2-jabatan {
  color: #e0e7ff;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 500;
  margin-bottom: 20px;
}

.home2-main {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 18px;
}

.home2-clock-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18px;
}

.home2-clock-box {
  background: #1880ff;
  border-radius: 10px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.13);
  font-size: 4rem;
  font-weight: 700;
  color: #ffffff;
  width: 130px;
  height: 110px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16px;
  transition: background 0.2s, color 0.2s, transform 0.2s;
}

.home2-clock-sep {
  font-size: 4rem;
  font-weight: 700;
  color: #222;
  margin: 0 4px;
}

.home2-camera-row {
  margin: 18px 0 24px 0;
  display: flex;
  justify-content: center;
}

.home2-camera-btn {
  background: #1880ff;
  border: none;
  border-radius: 50%;
  width: 130px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(24,128,255,0.18);
  cursor: pointer;
  transition: background 0.2s;
}
.home2-camera-btn:active {
  background: #0d5ec2;
}
.home2-camera-icon {
  color: #fff;
  font-size: 4rem;
}

.home2-menu-row {
  display: flex;
  justify-content: center;
  gap: 18px;
  margin-bottom: 28px;
}

.home2-menu-btn {
  background: #1880ff;
  border-radius: 22px;
  box-shadow: 0 6px 18px rgba(0,0,0,0.10);
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.3rem;
  color: #ffffff;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.home2-menu-btn:active {
  box-shadow: 0 2px 16px rgba(24,128,255,0.13);
}
.home2-menu-btn ion-icon {
  font-size: 2.5rem;
  margin-bottom: 8px;
}

.home2-status-wrap {
  width: 100%;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.home2-status-title {
  background: #1880ff;
  border-radius: 32px;
  padding: 16px 80px;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 5px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  min-width: 340px;
  width: max-content;
  max-width: 90vw;
  text-align: center;
}

.home2-status-row {
  display: flex;
  gap: 18px;
  justify-content: center;
  width: 100%;
}

.home2-status-box {
  background: #fff;
  border-radius: 28px;
  box-shadow: 0 6px 18px rgba(0,0,0,0.10);
  width: 180px;
  height: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: 700;
  color: #222;
}

.home2-status-label {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.home2-status-time {
  font-size: 2.1rem;
  font-weight: 700;
  color: #222;
}

.home2-header {
  width: 100vw;
  background: linear-gradient(135deg, #1880ff 60%, #005be7 100%);
  min-height: 200px;
  border-bottom-left-radius: 80px;
  border-bottom-right-radius: 80px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(24,128,255,0.13);
  position: relative;
  z-index: 2;
  animation: fadeInHeader 1.2s;
}

@keyframes fadeInHeader {
  from { opacity: 0; transform: translateY(-40px); }
  to { opacity: 1; transform: none; }
}

.home2-header-content {
  width: 100%;
  max-width: 420px;
  padding: 38px 0 22px 0;
  text-align: center;
  animation: fadeIn 1.2s;
}

@media (max-width: 480px) {
  .home-container {
    max-width: 98vw;
    padding-top: 8vw;
  }
  .home-profile-card {
    border-radius: 12px;
  }
  .home-avatar {
    width: 56px;
    height: 56px;
    margin-right: 12px;
  }
  .home-nama {
    font-size: 1.05rem;
  }
  .home-jabatan {
    font-size: 0.95rem;
  }
  .home2-header {
    min-height: 120px;
    border-bottom-left-radius: 38px;
    border-bottom-right-radius: 38px;
  }
  .home2-header-content {
    padding: 18px 0 8px 0;
  }
  .home2-nama {
    font-size: 1.2rem;
  }
  .home2-jabatan {
    font-size: 0.95rem;
  }
  .home2-main {
    padding-top: 10px;
  }
  .home2-clock-box {
    width: 100px;
    height: 60px;
    font-size: 2.1rem;
  }
  .home2-clock-sep {
    font-size: 1.6rem;
  }
  .home2-camera-btn {
    width: 110px;
    height: 110px;
  }
  .home2-camera-icon {
    font-size: 3.5rem;
  }
  .home2-menu-btn {
    width: 100px;
    height: 100px;
    font-size: 0.95rem;
  }
  .home2-menu-btn ion-icon {
    font-size: 2.3rem;
  }
  .home2-status-box {
    width: 200px;
    height: 200px;
    font-size: 0.95rem;
  }
  .home2-status-title {
    font-size: 1rem;
    padding: 10px 18px;
    min-width: 0;
    width: 100%;
    max-width: 100vw;
  }
  .home2-status-label {
    font-size: 1rem;
  }
  .home2-status-time {
    font-size: 1.2rem;
  }
}
