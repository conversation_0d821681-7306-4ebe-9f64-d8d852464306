# Alur Absensi Rapat dengan Barcode Scanner

## 🔄 **Alur Proses Absensi**

### 1. **Scan Barcode**
- User membuka halaman `/scan-rapat`
- Kamera otomatis aktif dan mendeteksi barcode
- Barcode value di-extract dari hasil scan

### 2. **Validasi Rapat**
```typescript
// API Call ke: https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023
const res = await fetch(API_RAPAT);
const json = await res.json();

// Cari rapat berdasarkan barcode_value
const rapatData = json.data.find((r: any) => r.barcode_value === data);
```

### 3. **Update Status Kehadiran**
```typescript
// Payload yang dikirim ke API Peserta
const pesertaPayload = {
  api_key: 'absensiku_api_key_2023',
  rapat_id: rapatData.id,
  user_id: user.id || user.nik,
  status: 'hadir',                    // ✅ OTOMATIS SET KE "HADIR"
  waktu_hadir: waktuHadir,           // ✅ OTOMATIS CATAT WAKTU SAAT INI
};

// API Call ke: https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023
const pesertaRes = await fetch(API_PESERTA, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(pesertaPayload),
});
```

## 📊 **Data yang Dikirim ke API**

### **Request ke API Rapat Peserta:**
```json
{
  "api_key": "absensiku_api_key_2023",
  "rapat_id": "123",
  "user_id": "USER_ID_ATAU_NIK",
  "status": "hadir",
  "waktu_hadir": "2024-07-24 12:30:45"
}
```

### **Expected Response:**
```json
{
  "status": "success",
  "message": "Absensi berhasil dicatat"
}
```

## 🎯 **Fitur Otomatis yang Sudah Diimplementasi**

### ✅ **Status Otomatis "HADIR"**
- Ketika barcode berhasil di-scan dan rapat ditemukan
- Status otomatis di-set ke `"hadir"`
- Tidak perlu input manual dari user

### ✅ **Waktu Hadir Otomatis**
- Menggunakan waktu real-time saat scan berhasil
- Format: `YYYY-MM-DD HH:mm:ss` (contoh: `2024-07-24 12:30:45`)
- Timezone mengikuti sistem perangkat user

### ✅ **Validasi User**
- Menggunakan `user.id` atau `user.nik` dari localStorage
- Validasi user sudah login sebelum mencatat absensi

## 🔍 **Debugging & Monitoring**

### **Console Logs untuk Tracking:**
```typescript
console.log('Processing barcode:', data);
console.log('Fetching rapat data from:', API_RAPAT);
console.log('Rapat API response:', json);
console.log('Found rapat data:', rapatData);
console.log('Sending peserta data:', pesertaPayload);
console.log('Peserta API response:', pesertaJson);
console.log('✅ Absensi berhasil dicatat');
```

### **Error Handling:**
- ❌ Barcode tidak ditemukan dalam data rapat
- ❌ User belum login (tidak ada user.id/nik)
- ❌ API rapat tidak response
- ❌ API peserta gagal update
- ❌ Network connection error

## 📱 **UI Feedback untuk User**

### **Loading State:**
```
"Memproses barcode: [BARCODE_VALUE]"
"Mencari data rapat dan mencatat kehadiran..."
```

### **Success State:**
```
✅ [NAMA_RAPAT]
📅 Tanggal: [TANGGAL]
🕐 Waktu: [WAKTU_MULAI] - [WAKTU_SELESAI]
📍 Lokasi: [LOKASI]
📝 Deskripsi: [DESKRIPSI]

✅ Status: HADIR
🕐 Waktu Absen: [WAKTU_REAL_TIME]
```

### **Error States:**
- "Barcode tidak valid untuk rapat yang tersedia!"
- "Data user tidak ditemukan. Silakan login ulang."
- "Gagal mencatat absensi rapat"
- "Terjadi kesalahan koneksi"

## 🧪 **Testing Checklist**

### **Scenario 1: Scan Valid Barcode**
1. ✅ Scan barcode yang valid
2. ✅ Sistem menemukan data rapat
3. ✅ API peserta berhasil update status "hadir"
4. ✅ Waktu hadir tercatat dengan benar
5. ✅ UI menampilkan konfirmasi sukses

### **Scenario 2: Scan Invalid Barcode**
1. ✅ Scan barcode yang tidak valid
2. ✅ Sistem tidak menemukan data rapat
3. ✅ UI menampilkan pesan error yang jelas
4. ✅ Tidak ada data yang ter-update di database

### **Scenario 3: Network Error**
1. ✅ Scan barcode saat offline/network error
2. ✅ UI menampilkan pesan error koneksi
3. ✅ User bisa retry setelah koneksi normal

### **Scenario 4: User Not Logged In**
1. ✅ Scan barcode tanpa data user
2. ✅ UI menampilkan pesan login ulang
3. ✅ Tidak ada data yang ter-update

## 🔧 **API Requirements**

### **API Rapat (GET):**
- Endpoint: `https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023`
- Method: GET
- Response harus include field `barcode_value` untuk matching

### **API Peserta (POST):**
- Endpoint: `https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023`
- Method: POST
- Content-Type: application/json
- Body: `{ api_key, rapat_id, user_id, status, waktu_hadir }`

## 📈 **Performance Metrics**

### **Expected Response Times:**
- Camera initialization: ~1-2 seconds
- Barcode detection: ~100-300ms
- API rapat call: ~500-1000ms
- API peserta call: ~500-1000ms
- Total process: ~2-4 seconds

### **Success Rate Targets:**
- Barcode detection: >95%
- API success rate: >98%
- Overall process success: >90%

## 🚀 **Deployment Notes**

### **Production Checklist:**
- ✅ HTTPS required untuk camera access
- ✅ Camera permissions di AndroidManifest.xml
- ✅ API endpoints accessible dari production domain
- ✅ Error logging untuk monitoring
- ✅ User feedback untuk semua scenarios

### **Monitoring:**
- Track scan success/failure rates
- Monitor API response times
- Log error patterns untuk improvement
- User feedback collection
